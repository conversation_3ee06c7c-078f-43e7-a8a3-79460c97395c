import asyncio
import json
import os
from datetime import datetime

from pyrogram import filters
from pyrogram.errors import <PERSON><PERSON><PERSON>

from config import BANNED_USERS, OWNER_ID
from Amusic import app
from Amusic.core.redis_db import redis_client


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()  # Convert datetime to ISO 8601 format
        return super().default(obj)


async def export_redis_data():
    """Export all Redis data to a JSON file"""
    data = {}

    # Get all keys from Redis
    keys = await redis_client.keys("*")

    for key in keys:
        key_str = key.decode('utf-8') if isinstance(key, bytes) else key

        # Get the type of the key
        key_type = await redis_client.type(key)
        key_type_str = key_type.decode('utf-8') if isinstance(key_type, bytes) else key_type

        if key_type_str == "string":
            value = await redis_client.get(key)
            try:
                # Try to parse as JSON
                data[key_str] = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # If not JSON, store as string
                data[key_str] = value.decode('utf-8') if isinstance(value, bytes) else value
        elif key_type_str == "hash":
            data[key_str] = await redis_client.hgetall(key)
        elif key_type_str == "list":
            data[key_str] = await redis_client.lrange(key, 0, -1)
        elif key_type_str == "set":
            data[key_str] = list(await redis_client.smembers(key))
        elif key_type_str == "zset":
            data[key_str] = await redis_client.zrange(key, 0, -1, withscores=True)

    # Ensure cache directory exists
    os.makedirs("cache", exist_ok=True)

    file_path = os.path.join("cache", f"redis_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(file_path, "w", encoding="utf-8") as backup_file:
        json.dump(data, backup_file, indent=4, cls=CustomJSONEncoder, ensure_ascii=False)

    return file_path


async def clear_redis_data():
    """Clear all Redis data"""
    await redis_client.flushdb()


async def edit_or_reply(mystic, text):
    try:
        return await mystic.edit_text(text, disable_web_page_preview=True)
    except FloodWait as e:
        await asyncio.sleep(e.value)
        return await mystic.edit_text(text, disable_web_page_preview=True)
    try:
        await mystic.delete()
    except Exception:
        pass
    return await app.send_message(mystic.chat.id, disable_web_page_preview=True)


@app.on_message(filters.command("export") & ~BANNED_USERS)
async def export_database(client, message):
    if message.from_user.id not in OWNER_ID:
        return

    mystic = await message.reply_text("Exporting Redis database...")

    try:
        # Export Redis data
        file_path = await export_redis_data()

        async def progress(current, total):
            try:
                await mystic.edit_text(f"Uploading.... {current * 100 / total:.1f}%")
            except FloodWait as e:
                await asyncio.sleep(e.value)

        # Send the backup file
        await app.send_document(
            message.chat.id,
            file_path,
            caption=f"Redis Backup of {app.mention}. You can import this by replying /import to this file.",
            progress=progress,
        )

        # Clean up the temporary file
        try:
            os.remove(file_path)
        except Exception:
            pass

        await mystic.delete()

    except Exception as e:
        await edit_or_reply(mystic, f"Error during export: {str(e)}")


@app.on_message(filters.command("import") & ~BANNED_USERS)
async def import_database(client, message):
    if message.from_user.id not in OWNER_ID:
        return

    if not message.reply_to_message or not message.reply_to_message.document:
        return await message.reply_text(
            "You need to reply to an exported Redis backup file to import it."
        )

    mystic = await message.reply_text("Downloading backup file...")

    async def progress(current, total):
        try:
            await mystic.edit_text(f"Downloading... {current * 100 / total:.1f}%")
        except FloodWait as w:
            await asyncio.sleep(w.value)

    file_path = await message.reply_to_message.download(progress=progress)

    try:
        with open(file_path, encoding="utf-8") as backup_file:
            data = json.load(backup_file)
    except (json.JSONDecodeError, OSError) as e:
        return await edit_or_reply(
            mystic, f"Invalid backup file format. Error: {str(e)}"
        )

    if not isinstance(data, dict):
        return await edit_or_reply(
            mystic, "Invalid backup file format. Expected JSON object."
        )

    try:
        # Clear existing Redis data (optional - you might want to comment this out)
        # await clear_redis_data()

        mystic = await edit_or_reply(mystic, "Importing Redis data...")

        # Import data back to Redis
        for key, value in data.items():
            if isinstance(value, dict):
                # Hash data
                await redis_client.hset(key, mapping=value)
            elif isinstance(value, list):
                if value and isinstance(value[0], tuple):
                    # Sorted set data (with scores)
                    for item, score in value:
                        await redis_client.zadd(key, {item: score})
                else:
                    # List data
                    await redis_client.delete(key)  # Clear existing list
                    if value:
                        await redis_client.lpush(key, *reversed(value))
            elif isinstance(value, set):
                # Set data
                if value:
                    await redis_client.sadd(key, *value)
            else:
                # String data (including JSON strings)
                if isinstance(value, (dict, list)):
                    await redis_client.set(key, json.dumps(value))
                else:
                    await redis_client.set(key, str(value))

        await edit_or_reply(mystic, "✅ Data successfully imported from backup file!")

    except Exception as e:
        await edit_or_reply(mystic, f"❌ Error during import: {str(e)}")

    # Clean up downloaded file
    if os.path.exists(file_path):
        os.remove(file_path)
