#
import asyncio
import os

from pyrogram import idle
from pytgcalls.exceptions import NoActiveGroupCall

import config
from config import BANNED_USERS
from Amusic import HELPABLE, LOGGER, app, userbot
from Amusic.core.call import Amusic
from Amusic.core.redis_db import init_redis
from Amusic.misc import sudo
from Amusic.utils.database import get_banned_users, get_gbanned

logger = LOGGER("Amusic")
loop = asyncio.get_event_loop()


async def init():
    # Initialize Redis database
    await init_redis()

    if len(config.STRING_SESSIONS) == 0:
        logger.error("No Assistant Clients Vars Defined!.. Exiting Process.")
        return
    if not config.SPOTIFY_CLIENT_ID and not config.SPOTIFY_CLIENT_SECRET:
        logger.warning(
            "No Spotify Vars defined. Your bot won't be able to play spotify queries."
        )
    try:
        users = await get_gbanned()
        for user_id in users:
            BANNED_USERS.add(user_id)
        users = await get_banned_users()
        for user_id in users:
            BANNED_USERS.add(user_id)
    except Exception:
        pass
    await sudo()
    await app.start()
    for mod in app.load_plugins_from("Amusic/plugins"):
        if mod and hasattr(mod, "__MODULE__") and mod.__MODULE__:
            if hasattr(mod, "__HELP__") and mod.__HELP__:
                HELPABLE[mod.__MODULE__.lower()] = mod

    if config.EXTRA_PLUGINS:
        if os.path.exists("xtraplugins"):
            result = await app.run_shell_command(["git", "-C", "xtraplugins", "pull"])
            if result["returncode"] != 0:
                logger.error(
                    f"Error pulling updates for extra plugins: {result['stderr']}"
                )
                exit()
        else:
            result = await app.run_shell_command(
                ["git", "clone", config.EXTRA_PLUGINS_REPO, "xtraplugins"]
            )
            if result["returncode"] != 0:
                logger.error(f"Error cloning extra plugins: {result['stderr']}")
                exit()

        req = os.path.join("xtraplugins", "requirements.txt")
        if os.path.exists(req):
            result = await app.run_shell_command(
                ["uv", "pip", "install", "--system", "-r", req]
            )
            if result["returncode"] != 0:
                logger.error(f"Error installing requirements: {result['stderr']}")

        for mod in app.load_plugins_from("xtraplugins"):
            if mod and hasattr(mod, "__MODULE__") and mod.__MODULE__:
                if hasattr(mod, "__HELP__") and mod.__HELP__:
                    HELPABLE[mod.__MODULE__.lower()] = mod

    LOGGER("Amusic.plugins").info("Successfully Imported All Modules ")
    await userbot.start()
    await Amusic.start()
    LOGGER("Amusic").info("Assistant Started Sucessfully")
    try:
        await Amusic.stream_call(
            "http://docs.evostream.com/sample_content/assets/sintel1m720p.mp4"
        )
    except NoActiveGroupCall:
        LOGGER("Amusic").error(
            "Please ensure the voice call in your log group is active."
        )
        exit()

    await Amusic.decorators()
    LOGGER("Amusic").info("Amusic Started Successfully")
    await idle()
    await app.stop()
    await userbot.stop()
    await Amusic.stop()


def main():
    loop.run_until_complete(init())
    LOGGER("Amusic").info("Stopping Amusic! GoodBye")


if __name__ == "__main__":
    main()
