#
import asyncio as _asyncio

import uvloop as _uvloop

_asyncio.set_event_loop_policy(_uvloop.EventLoopPolicy())  # noqa

from Amusic.core.bot import AmusicBot
from Amusic.core.dir import dirr
from Amusic.core.git import git
from Amusic.core.userbot import Userbot
from Amusic.misc import dbb, heroku

from .logging import LOGGER

# Directories
dirr()

# Check Git Updates
git()

# Initialize Memory DB
dbb()

# Heroku APP
heroku()

app = AmusicBot()
userbot = Userbot()

HELPABLE = {}
