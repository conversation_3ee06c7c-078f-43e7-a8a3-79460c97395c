from pyrogram import Client
import asyncio

API_ID = 27396410
API_HASH = "175dd5dc67ce353d41d7aefd5a104d9c"
STRING_SESSION = "BAGiCToAxRV_JTLGrJRpxXmi_9X1OI9uhQ9pgkELK_wPKvobHImeXOf8JG4VaVL1K2vdfY24gbeoBARm5sqdniZ4MTPk7CEYIhgHt2re2_febHiRC8cZMcNv06GV55Pgrp8dqTRwQuusuoYItEHvWU8SBRFY-a0e492rOiLhVYT6BtrhR7luXFR_SafEIFdVUD22e1ORhvFd6H-_TG8fwfUEBSwOhOp5wkt4cmjcJ7tna465_2HnXEMvetD6mY9tMg9XtHPkxbGCvmiZxKrtDI5VFns1Ul0j2LdIweKvbQ3wQBfMhvton6_1GOM2vtl21HwK1QgnLM6zxJ8ILVxKcd0cYHTa8wAAAAGiRtphAA"

user = Client(
    "user111",
    api_id=API_ID,
    api_hash=API_HASH,
    session_string=STRING_SESSION,
)

async def main():
    await user.start()
    me = await user.get_me()
    print(me)
    async for dialog in user.get_dialogs(limit=-1):
        print(dialog)

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())
    
