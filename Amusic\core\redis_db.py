#
# Redis implementation for local database with bot ID isolation
import json
import logging
import sys
from typing import Any, Dict, List, Optional

import redis.asyncio as redis

import config

logger = logging.getLogger(__name__)

# Redis connection
redis_client = None

class RedisDatabase:
    def __init__(self, bot_id: str = "default"):
        self.bot_id = bot_id
        self.prefix = f"amusic:{bot_id}:"

    def _get_key(self, collection: str, doc_id: str = None) -> str:
        """Generate Redis key with bot ID isolation"""
        if doc_id:
            return f"{self.prefix}{collection}:{doc_id}"
        return f"{self.prefix}{collection}"

    def _get_list_key(self, collection: str) -> str:
        """Generate Redis key for list operations"""
        return f"{self.prefix}{collection}:list"

    async def find_one(self, collection: str, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find one document in collection"""
        if not redis_client:
            return None

        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            data = await redis_client.get(redis_key)
            if data:
                return json.loads(data)
        return None

    async def find(self, collection: str, query: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Find multiple documents in collection"""
        if not redis_client:
            return []

        results = []
        pattern = f"{self.prefix}{collection}:*"

        async for key in redis_client.scan_iter(match=pattern):
            if key.endswith(":list"):
                continue
            data = await redis_client.get(key)
            if data:
                doc = json.loads(data)
                # Apply query filter if provided
                if query:
                    match = True
                    for field, value in query.items():
                        if isinstance(value, dict):
                            # Handle MongoDB-style operators
                            if "$gt" in value:
                                if field not in doc or doc[field] <= value["$gt"]:
                                    match = False
                                    break
                            elif "$lt" in value:
                                if field not in doc or doc[field] >= value["$lt"]:
                                    match = False
                                    break
                        else:
                            if field not in doc or doc[field] != value:
                                match = False
                                break
                    if match:
                        results.append(doc)
                else:
                    results.append(doc)

        return results

    async def update_one(self, collection: str, query: Dict[str, Any], update: Dict[str, Any], upsert: bool = False) -> bool:
        """Update one document in collection"""
        if not redis_client:
            return False

        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            # Get existing data or create new
            existing_data = await redis_client.get(redis_key)
            if existing_data:
                doc = json.loads(existing_data)
            elif upsert:
                doc = query.copy()
            else:
                return False

            # Apply $set operations
            if "$set" in update:
                doc.update(update["$set"])

            # Save back to Redis
            await redis_client.set(redis_key, json.dumps(doc))
            return True
        return False

    async def insert_one(self, collection: str, document: Dict[str, Any]) -> bool:
        """Insert one document into collection"""
        if not redis_client:
            return False

        # Use first field as identifier
        if document:
            key_field = list(document.keys())[0]
            key_value = str(document[key_field])
            redis_key = self._get_key(collection, key_value)

            await redis_client.set(redis_key, json.dumps(document))
            return True
        return False

    async def delete_one(self, collection: str, query: Dict[str, Any]) -> bool:
        """Delete one document from collection"""
        if not redis_client:
            return False

        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            result = await redis_client.delete(redis_key)
            return result > 0
        return False

# Initialize Redis connection
async def init_redis():
    global redis_client
    try:
        redis_client = redis.Redis(
            host='localhost',
            port=6379,
            decode_responses=False,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        await redis_client.ping()
        logger.info("Redis connection established successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        logger.error("Please ensure Redis is running on localhost:6379")
        sys.exit(1)

# Create database instance with bot ID isolation
BOT_ID = getattr(config, 'BOT_TOKEN', 'default').split(':')[0] if hasattr(config, 'BOT_TOKEN') and config.BOT_TOKEN else 'default'
redis_db = RedisDatabase(BOT_ID)

# Compatibility layer for existing MongoDB code
class MongoCompatibility:
    def __init__(self, db_instance):
        self.db = db_instance

    @property
    def queries(self):
        return CollectionWrapper(self.db, "queries")

    @property
    def userstats(self):
        return CollectionWrapper(self.db, "userstats")

    @property
    def chatstats(self):
        return CollectionWrapper(self.db, "chatstats")

    @property
    def authuser(self):
        return CollectionWrapper(self.db, "authuser")

    @property
    def assistants(self):
        return CollectionWrapper(self.db, "assistants")

    @property
    def gban(self):
        return CollectionWrapper(self.db, "gban")

    @property
    def sudoers(self):
        return CollectionWrapper(self.db, "sudoers")

    @property
    def chats(self):
        return CollectionWrapper(self.db, "chats")

    @property
    def blacklistChat(self):
        return CollectionWrapper(self.db, "blacklistChat")

    @property
    def tgusersdb(self):
        return CollectionWrapper(self.db, "tgusersdb")

    @property
    def playlist(self):
        return CollectionWrapper(self.db, "playlist")

    @property
    def blockedusers(self):
        return CollectionWrapper(self.db, "blockedusers")

    @property
    def privatechats(self):
        return CollectionWrapper(self.db, "privatechats")

    @property
    def cplaymode(self):
        return CollectionWrapper(self.db, "cplaymode")

    @property
    def commands(self):
        return CollectionWrapper(self.db, "commands")

    @property
    def cleanmode(self):
        return CollectionWrapper(self.db, "cleanmode")

    @property
    def playmode(self):
        return CollectionWrapper(self.db, "playmode")

    @property
    def playtypedb(self):
        return CollectionWrapper(self.db, "playtypedb")

    @property
    def language(self):
        return CollectionWrapper(self.db, "language")

    @property
    def adminauth(self):
        return CollectionWrapper(self.db, "adminauth")

    @property
    def amusic_videocalls(self):
        return CollectionWrapper(self.db, "amusic_videocalls")

    @property
    def onoffper(self):
        return CollectionWrapper(self.db, "onoffper")

    @property
    def autoend(self):
        return CollectionWrapper(self.db, "autoend")

    @property
    def notes(self):
        return CollectionWrapper(self.db, "notes")

    @property
    def filters(self):
        return CollectionWrapper(self.db, "filters")

class CollectionWrapper:
    def __init__(self, db_instance, collection_name):
        self.db = db_instance
        self.collection_name = collection_name

    async def find_one(self, query):
        return await self.db.find_one(self.collection_name, query)

    async def find(self, query=None):
        """Return async iterator for find operations"""
        results = await self.db.find(self.collection_name, query)
        for result in results:
            yield result

    async def update_one(self, query, update, upsert=False):
        return await self.db.update_one(self.collection_name, query, update, upsert)

    async def insert_one(self, document):
        return await self.db.insert_one(self.collection_name, document)

    async def delete_one(self, query):
        return await self.db.delete_one(self.collection_name, query)

# Create compatibility instance
mongodb = MongoCompatibility(redis_db)
