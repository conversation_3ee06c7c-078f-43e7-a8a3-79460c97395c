

import asyncio
import time

from py_yt import VideosSearch
from pyrogram import filters
from pyrogram.enums import ChatType, ParseMode
from pyrogram.types import InlineKeyboardButton, InlineKeyboardMarkup, Message

import config
from config import BANNED_USERS, START_IMG_URL
from config.config import OWNER_ID
from strings import command, get_command, get_string
from Amusic import app
from Amusic.misc import SUDOERS, _boot_
from Amusic.platforms import telegram, youtube
from Amusic.plugins.bot.help import paginate_modules
from Amusic.plugins.play.playlist import del_plist_msg
from Amusic.plugins.sudo.sudoers import sudoers_list
from Amusic.utils.database import (
    add_served_chat,
    add_served_user,
    blacklisted_chats,
    get_assistant,
    get_lang,
    get_userss,
    is_on_off,
    is_served_private_chat,
)
from Amusic.utils.decorators import LanguageStart, asyncify
from Amusic.utils.formatters import get_readable_time
from Amusic.utils.functions import <PERSON><PERSON><PERSON><PERSON>OW<PERSON>, WELCOMEHELP
from Amusic.utils.inline import private_panel, start_pannel


@app.on_message(
    command("START_COMMAND") & filters.private & ~BANNED_USERS
)
@LanguageStart
async def start_comm(client, message: Message, _):
    chat_id = message.chat.id
    await add_served_user(message.from_user.id)
    if len(message.text.split()) > 1:
        name = message.text.split(None, 1)[1]
        if name[0:4] == "help":
            keyboard = await paginate_modules(0, chat_id, close=True)

            if config.START_IMG_URL:
                return await message.reply_photo(
                    photo=START_IMG_URL,
                    caption=_["help_1"],
                    reply_markup=keyboard,
                )
            else:
                return await message.reply_text(
                    text=_["help_1"],
                    reply_markup=keyboard,
                )
        if name[0:4] == "song":
            await message.reply_text(_["song_2"])
            return
        if name == "mkdwn_help":
            await message.reply(
                MARKDOWN,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
            )
        if name == "greetings":
            await message.reply(
                WELCOMEHELP,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
            )
        if name[0:3] == "sta":
            m = await message.reply_text("🔎 Fetching Your personal stats.!")
            stats = await get_userss(message.from_user.id)
            tot = len(stats)
            if not stats:
                return await m.edit(_["ustats_1"])

            @asyncify
            def get_stats():
                msg = ""
                limit = 0
                results = {}
                for i in stats:
                    top_list = stats[i]["spot"]
                    results[str(i)] = top_list
                    list_arranged = dict(
                        sorted(
                            results.items(),
                            key=lambda item: item[1],
                            reverse=True,
                        )
                    )
                if not results:
                    return m.edit(_["ustats_1"])
                tota = 0
                videoid = None
                for vidid, count in list_arranged.items():
                    tota += count
                    if limit == 10:
                        continue
                    if limit == 0:
                        videoid = vidid
                    limit += 1
                    details = stats.get(vidid)
                    title = (details["title"][:35]).title()
                    if vidid == "telegram":
                        msg += f"🔗[Telegram Files and Audio]({config.SUPPORT_GROUP}) ** played {count} Times**\n\n"
                    else:
                        msg += f"🔗 [{title}](https://www.youtube.com/watch?v={vidid}) ** played {count} Times**\n\n"
                msg = _["ustats_2"].format(tot, tota, limit) + msg
                return videoid, msg

            try:
                videoid, msg = await get_stats()
            except Exception as e:
                print(e)
                return
            thumbnail = await youtube.thumbnail(videoid, True)
            await m.delete()
            await message.reply_photo(photo=thumbnail, caption=msg)
            return
        if name[0:3] == "sud":
            await sudoers_list(client=client, message=message, _=_)
            await asyncio.sleep(1)
            if await is_on_off(config.LOG):
                sender_id = message.from_user.id
                message.from_user.mention
                sender_name = message.from_user.first_name
                return await app.send_message(
                    config.LOG_GROUP_ID,
                    f"{message.from_user.mention} Has just started bot to check <code>Sudolist </code>\n\n**User Id:** {sender_id}\n**User Name:** {sender_name}",
                )
            return
        if name[0:3] == "lyr":
            query = (str(name)).replace("lyrics_", "", 1)
            lyrical = config.lyrical
            lyrics = lyrical.get(query)
            if lyrics:
                await telegram.send_split_text(message, lyrics)
                return
            else:
                await message.reply_text("Failed to get lyrics ")
                return
        if name[0:3] == "del":
            await del_plist_msg(client=client, message=message, _=_)
            await asyncio.sleep(1)
        if name[0:3] == "inf":
            m = await message.reply_text("🔎 Fetching info..")
            query = (str(name)).replace("info_", "", 1)
            query = f"https://www.youtube.com/watch?v={query}"
            results = VideosSearch(query, limit=1)
            for result in (await results.next())["result"]:
                title = result["title"]
                duration = result["duration"]
                views = result["viewCount"]["short"]
                thumbnail = result["thumbnails"][0]["url"].split("?")[0]
                channellink = result["channel"]["link"]
                channel = result["channel"]["name"]
                link = result["link"]
                published = result["publishedTime"]
            searched_text = f"""
🔍__**Video track information **__

❇️**Title:** {title}

⏳**Duration:** {duration} Mins
👀**Views:** `{views}`
⏰**Published times:** {published}
🎥**Channel Name:** {channel}
📎**Channel Link:** [Visit from here]({channellink})
🔗**Video Link:** [Link]({link})
"""
            key = InlineKeyboardMarkup(
                [
                    [
                        InlineKeyboardButton(text="🎥 Watch ", url=f"{link}"),
                        InlineKeyboardButton(text="🔄 Close", callback_data="close"),
                    ],
                ]
            )
            await m.delete()
            await app.send_photo(
                message.chat.id,
                photo=thumbnail,
                caption=searched_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=key,
            )
            await asyncio.sleep(1)
            if await is_on_off(config.LOG):
                sender_id = message.from_user.id
                sender_name = message.from_user.first_name
                return await app.send_message(
                    config.LOG_GROUP_ID,
                    f"{message.from_user.mention} Has just started bot ot check <code> Video information  </code>\n\n**User Id:** {sender_id}\n**User Name** {sender_name}",
                )
    else:
        try:
            await app.resolve_peer(OWNER_ID[0])
            OWNER = OWNER_ID[0]
        except Exception:
            OWNER = None

        
        user_id = message.from_user.id
        is_developer = user_id in SUDOERS or user_id in OWNER_ID

        if is_developer:
            
            uptime = int(time.time() - _boot_)
            uptime_text = get_readable_time(uptime)
            text = _["start_developer"].format(uptime_text)
            out = []  
        else:
            
            text = _["start_1"].format(app.mention)
            out = private_panel(_, app.username, OWNER)

        if config.START_IMG_URL:
            try:
                await message.reply_photo(
                    photo=config.START_IMG_URL,
                    caption=text,
                    reply_markup=InlineKeyboardMarkup(out) if out else None,
                )
            except Exception:
                await message.reply_text(
                    text=text,
                    reply_markup=InlineKeyboardMarkup(out) if out else None,
                )
        else:
            await message.reply_text(
                text=text,
                reply_markup=InlineKeyboardMarkup(out) if out else None,
            )
        if await is_on_off(config.LOG):
            sender_id = message.from_user.id
            sender_name = message.from_user.first_name
            return await app.send_message(
                config.LOG_GROUP_ID,
                f"{message.from_user.mention} Has started bot. \n\n**User id :** {sender_id}\n**User name:** {sender_name}",
            )


@app.on_message(command("START_COMMAND") & filters.group & ~BANNED_USERS)
@LanguageStart
async def testbot(client, message: Message, _):
    uptime = int(time.time() - _boot_)
    message.chat.id
    await message.reply_text(_["start_7"].format(get_readable_time(uptime)))

    return await add_served_chat(message.chat.id)


@app.on_message(filters.new_chat_members, group=-1)
async def welcome(client, message: Message):
    chat_id = message.chat.id
    if config.PRIVATE_BOT_MODE:
        if not await is_served_private_chat(message.chat.id):
            await message.reply_text(
                "This Bot's private mode has been enabled only my owner can use this if want to use in your chat so say my Owner to authorize your chat."
            )
            return await app.leave_chat(message.chat.id)
    else:
        await add_served_chat(chat_id)
    for member in message.new_chat_members:
        try:
            language = await get_lang(message.chat.id)
            _ = get_string(language)
            if member.id == app.id:
                chat_type = message.chat.type
                if chat_type != ChatType.SUPERGROUP:
                    await message.reply_text(_["start_5"])
                    return await app.leave_chat(message.chat.id)
                if chat_id in await blacklisted_chats():
                    await message.reply_text(
                        _["start_6"].format(
                            f"https://t.me/{app.username}?start=sudolist"
                        )
                    )
                    return await app.leave_chat(chat_id)
                userbot = await get_assistant(message.chat.id)
                out = start_pannel(_)
                await message.reply_text(
                    _["start_2"].format(
                        app.mention,
                        userbot.username,
                        userbot.id,
                    ),
                    reply_markup=InlineKeyboardMarkup(out),
                )
            if member.id in config.OWNER_ID:
                return await message.reply_text(
                    _["start_3"].format(app.mention, member.mention)
                )
            if member.id in SUDOERS:
                return await message.reply_text(
                    _["start_4"].format(app.mention, member.mention)
                )
            return
        except Exception:
            return



@app.on_callback_query(filters.regex("features_cb"))
async def features_callback(client, query):
    """عرض مميزات البوت"""
    try:
        language = await get_lang(query.message.chat.id)
        _ = get_string(language)
    except Exception:
        _ = get_string("ar")

    features_text = """🌟 **مميزات البوت الخاصة** 🌟

🎵 **جودة صوت عالية:**
• تشغيل بجودة 320kbps
• دعم الصوت المحيطي
• تقليل الضوضاء التلقائي

🎧 **منصات متعددة:**
• يوتيوب بجودة عالية
• سبوتيفاي المتميز
• ساوندكلاود الأصلي
• ملفات تيليجرام المحلية

⚡ **سرعة فائقة:**
• تشغيل فوري للأغاني
• بحث ذكي ومتطور
• ذاكرة تخزين مؤقت محسنة

🎛️ **تحكم متقدم:**
• قوائم تشغيل ذكية
• تكرار وخلط الأغاني
• تحكم في مستوى الصوت

🔒 **أمان وخصوصية:**
• حماية من السبام
• تشفير البيانات
• عدم حفظ المعلومات الشخصية

🌍 **واجهة عربية:**
• ترجمة كاملة للعربية
• دعم فني باللغة العربية
• أوامر سهلة ومفهومة"""

    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("🔙 العودة", callback_data="start_back")]
    ])

    await query.edit_message_text(features_text, reply_markup=keyboard)



@app.on_callback_query(filters.regex("how_to_use"))
async def how_to_use_callback(client, query):
    """شرح كيفية استخدام البوت"""
    try:
        language = await get_lang(query.message.chat.id)
        _ = get_string(language)
    except Exception:
        _ = get_string("ar")

    usage_text = """❓ **كيفية استخدام البوت** ❓

📋 **الخطوات الأساسية:**

1️⃣ **إضافة البوت للمجموعة:**
   • اضغط على "أضفني إلى مجموعتك"
   • اختر المجموعة المطلوبة
   • امنح البوت صلاحيات الإدارة

2️⃣ **بدء التشغيل:**
   • استخدم الأمر: `/تشغيل اسم الأغنية`
   • أو أرسل رابط يوتيوب مباشرة
   • أو أرسل ملف صوتي

3️⃣ **أوامر التحكم الأساسية:**
   • `/ايقاف` - إيقاف مؤقت
   • `/استكمال` - استكمال التشغيل
   • `/تخطي` - الانتقال للأغنية التالية
   • `/انهاء` - إنهاء التشغيل

4️⃣ **أوامر متقدمة:**
   • `/قائمة` - عرض قائمة التشغيل
   • `/خلط` - خلط الأغاني
   • `/تكرار` - تكرار الأغنية الحالية

💡 **نصائح مهمة:**
• تأكد من وجود البوت في المكالمة الصوتية
• استخدم الأوامر في المجموعة وليس الخاص
• للحصول على أفضل جودة، استخدم روابط يوتيوب عالية الجودة"""

    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("📋 قائمة الأوامر", callback_data="settings_back_helper"),
            InlineKeyboardButton("🔙 العودة", callback_data="start_back")
        ]
    ])

    await query.edit_message_text(usage_text, reply_markup=keyboard)



@app.on_callback_query(filters.regex("start_back"))
async def start_back_callback(client, query):
    """العودة لرسالة الستارت الرئيسية"""
    try:
        await app.resolve_peer(OWNER_ID[0])
        OWNER = OWNER_ID[0]
    except Exception:
        OWNER = None

    try:
        language = await get_lang(query.message.chat.id)
        _ = get_string(language)
    except Exception:
        _ = get_string("ar")

    
    user_id = query.from_user.id
    is_developer = user_id in SUDOERS or user_id in OWNER_ID

    if is_developer:
        
        uptime = int(time.time() - _boot_)
        uptime_text = get_readable_time(uptime)
        text = _["start_developer"].format(uptime_text)
        out = []  
    else:
        
        text = _["start_1"].format(app.mention)
        out = private_panel(_, app.username, OWNER)

    await query.edit_message_text(
        text=text,
        reply_markup=InlineKeyboardMarkup(out) if out else None
    )
