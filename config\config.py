#
import json as _json
import os as _os
import re as _re
import sys as _sys

from pyrogram import filters as _flt

# Global variable to store configuration data
_config_data = None


def load_config():
    """Load configuration from config.json file"""
    global _config_data
    if _config_data is None:
        config_path = "config.json"
        try:
            if _os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    _config_data = _json.load(f)
            else:
                print(f"[WARNING] - Configuration file {config_path} not found. Using default values.")
                _config_data = {}
        except _json.JSONDecodeError as e:
            print(f"[ERROR] - Invalid JSON in {config_path}: {e}")
            print("[ERROR] - Please check your configuration file syntax.")
            _sys.exit(1)
        except Exception as e:
            print(f"[ERROR] - Failed to load configuration: {e}")
            _sys.exit(1)
    return _config_data


def is_bool(value: str) -> bool:
    return str(value).lower() in ["true", "yes"]


def parse_list(text: str, sep: str = ",") -> list[str]:
    if not text:
        text = ""
    return [v.strip() for v in str(text).strip("'\"").split(sep) if v.strip()]


def get_json(key, default=None):
    """Get configuration value from JSON file with fallback to default"""
    config = load_config()
    value = config.get(key, default)
    # Handle empty strings for required fields
    if value == "" and default is not None:
        return default
    return value


# Get it from my.telegram.org

API_ID = int(get_json("API_ID", "0"))

API_HASH = get_json("API_HASH", "")


# Get it from @Botfather in Telegram.
BOT_TOKEN = get_json("BOT_TOKEN", "")


# Database configuration - Using local Redis instead of MongoDB
# Redis runs locally on localhost:6379 (no configuration needed)

# Your cookies pasted link on batbin.me
# you can skip if you are adding cookies
# manually in config/cookies dir

COOKIE_LINK = parse_list(get_json("COOKIE_LINK", ""))

CLEANMODE_DELETE_MINS = int(
    get_json("CLEANMODE_MINS", "5")
)  # Remember to give value in Minute


# Custom max audio(music) duration for voice chat. set DURATION_LIMIT in variables with your own time(mins), Default to 60 mins.

DURATION_LIMIT_MIN = int(
    get_json("DURATION_LIMIT", "300")
)  # Remember to give value in Minutes


EXTRA_PLUGINS = is_bool(get_json("EXTRA_PLUGINS", "False"))

# Fill False if you Don't want to load extra plugins


EXTRA_PLUGINS_REPO = get_json(
    "EXTRA_PLUGINS_REPO",
    "",
)
# Fill here the external plugins repo where plugins that you want to load


# Duration Limit for downloading Songs in MP3 or MP4 format from bot
SONG_DOWNLOAD_DURATION = int(
    get_json("SONG_DOWNLOAD_DURATION_LIMIT", "90")
)  # Remember to give value in Minutes


# You'll need a Group ID or USERNAME for this.
LOG_GROUP_ID = get_json("LOG_GROUP_ID", "").strip()

# Your User ID.
OWNER_ID = list(
    map(int, get_json("OWNER_ID", "6815918609").split())
)  # Input type must be interger

# Git and deployment configurations removed for Ubuntu server deployment


# Only  Links formats are  accepted for this Var value.
SUPPORT_CHANNEL = get_json(
    "SUPPORT_CHANNEL", ""
)
SUPPORT_GROUP = get_json(
    "SUPPORT_GROUP", ""
)


# Set it in True if you want to leave your assistant after a certain amount of time. [Set time via AUTO_LEAVE_ASSISTANT_TIME]
AUTO_LEAVING_ASSISTANT = is_bool(get_json("AUTO_LEAVING_ASSISTANT", "False"))

# Time after which you're assistant account will leave chats automatically.
AUTO_LEAVE_ASSISTANT_TIME = int(
    get_json("ASSISTANT_LEAVE_TIME", 5800)
)  # Remember to give value in Seconds


# Set it true if you want your bot to be private only [You'll need to allow CHAT_ID via /authorize command then only your bot will play music in that chat.]
PRIVATE_BOT_MODE = is_bool(get_json("PRIVATE_BOT_MODE", "False"))


# Time sleep duration For Youtube Downloader
YOUTUBE_DOWNLOAD_EDIT_SLEEP = int(get_json("YOUTUBE_EDIT_SLEEP", "3"))

# Time sleep duration For Telegram Downloader
TELEGRAM_DOWNLOAD_EDIT_SLEEP = int(get_json("TELEGRAM_EDIT_SLEEP", "5"))


# GitHub repository configuration removed for Ubuntu server deployment


# Spotify Client.. Get it from https://developer.spotify.com/dashboard
SPOTIFY_CLIENT_ID = get_json("SPOTIFY_CLIENT_ID", "19609edb1b9f4ed7be0c8c1342039362")
SPOTIFY_CLIENT_SECRET = get_json(
    "SPOTIFY_CLIENT_SECRET", "409e31d3ddd64af08cfcc3b0f064fcbe"
)


# Maximum number of video calls allowed on bot. You can later set it via /set_video_limit on telegram
VIDEO_STREAM_LIMIT = int(get_json("VIDEO_STREAM_LIMIT", "999"))


# Maximum Limit Allowed for users to save playlists on bot's server
SERVER_PLAYLIST_LIMIT = int(get_json("SERVER_PLAYLIST_LIMIT", "25"))

# MaximuM limit for fetching playlist's track from youtube, spotify, apple links.
PLAYLIST_FETCH_LIMIT = int(get_json("PLAYLIST_FETCH_LIMIT", "25"))


# Telegram audio  and video file size limit

TG_AUDIO_FILESIZE_LIMIT = int(
    get_json("TG_AUDIO_FILESIZE_LIMIT", "1073741824")
)  # Remember to give value in bytes

TG_VIDEO_FILESIZE_LIMIT = int(
    get_json("TG_VIDEO_FILESIZE_LIMIT", "1073741824")
)  # Remember to give value in bytes

# Chceckout https://www.gbmb.org/mb-to-bytes  for converting mb to bytes


# If you want your bot to setup the commands automatically in the bot's menu set it to true.
# Refer to https://i.postimg.cc/Bbg3LQTG/image.png
SET_CMDS = is_bool(get_json("SET_CMDS", "False"))


# You'll need a Pyrogram String Session for these vars. See config/README.md for more information.
# Get the environment variable with a default value of an empty
STRING_SESSIONS = parse_list(get_json("STRING_SESSIONS", ""))

# DONT TOUCH or EDIT codes after this line
BANNED_USERS = _flt.user()
YTDOWNLOADER = 1
LOG = 2
LOG_FILE_NAME = "logs.txt"
adminlist = {}
lyrical = {}
chatstats = {}
userstats = {}
clean = {}

autoclean = []


# Images

START_IMG_URL = get_json(
    "START_IMG_URL",
    "https://te.legra.ph/file/4ec5ae4381dffb039b4ef.jpg",
)

PING_IMG_URL = get_json(
    "PING_IMG_URL",
    "https://telegra.ph/file/91533956c91d0fd7c9f20.jpg",
)

PLAYLIST_IMG_URL = get_json(
    "PLAYLIST_IMG_URL",
    "https://envs.sh/W_z.jpg",
)

GLOBAL_IMG_URL = get_json(
    "GLOBAL_IMG_URL",
    "https://telegra.ph/file/de1db74efac1770b1e8e9.jpg",
)

STATS_IMG_URL = get_json(
    "STATS_IMG_URL",
    "https://telegra.ph/file/4dd9e2c231eaf7c290404.jpg",
)

TELEGRAM_AUDIO_URL = get_json(
    "TELEGRAM_AUDIO_URL",
    "https://envs.sh/npk.jpg",
)

TELEGRAM_VIDEO_URL = get_json(
    "TELEGRAM_VIDEO_URL",
    "https://telegra.ph/file/8d02ff3bde400e465219a.jpg",
)

STREAM_IMG_URL = get_json(
    "STREAM_IMG_URL",
    "https://envs.sh/nAw.jpg",
)

SOUNCLOUD_IMG_URL = get_json(
    "SOUNCLOUD_IMG_URL",
    "https://envs.sh/nAD.jpg",
)

YOUTUBE_IMG_URL = get_json(
    "YOUTUBE_IMG_URL",
    "https://envs.sh/npl.jpg",
)

SPOTIFY_ARTIST_IMG_URL = get_json(
    "SPOTIFY_ARTIST_IMG_URL",
    "https://envs.sh/nA9.jpg",
)

SPOTIFY_ALBUM_IMG_URL = get_json(
    "SPOTIFY_ALBUM_IMG_URL",
    "https://envs.sh/nps.jpg",
)

SPOTIFY_PLAYLIST_IMG_URL = get_json(
    "SPOTIFY_PLAYLIST_IMG_URL",
    "https://telegra.ph/file/f4edfbd83ec3150284aae.jpg",
)


def time_to_seconds(time):
    stringt = str(time)
    return sum(int(x) * 60**i for i, x in enumerate(reversed(stringt.split(":"))))


def seconds_to_time(seconds):
    minutes = seconds // 60
    remaining_seconds = seconds % 60
    return f"{minutes:02d}:{remaining_seconds:02d}"


DURATION_LIMIT = int(time_to_seconds(f"{DURATION_LIMIT_MIN}:00"))
SONG_DOWNLOAD_DURATION_LIMIT = int(time_to_seconds(f"{SONG_DOWNLOAD_DURATION}:00"))

# Check if LOG_GROUP_ID is a numeric ID
if LOG_GROUP_ID.lstrip("-").isdigit():
    LOG_GROUP_ID = int(LOG_GROUP_ID)

if SUPPORT_CHANNEL:
    if not _re.match("(?:http|https)://", SUPPORT_CHANNEL):
        print(
            "[ERROR] - Your SUPPORT_CHANNEL url is wrong. Please ensure that it starts with https://"
        )
        _sys.exit()

if SUPPORT_GROUP:
    if not _re.match("(?:http|https)://", SUPPORT_GROUP):
        print(
            "[ERROR] - Your SUPPORT_GROUP url is wrong. Please ensure that it starts with https://"
        )
        _sys.exit()

# GitHub and upstream repository validation removed for Ubuntu server deployment


if PING_IMG_URL:
    if PING_IMG_URL != "https://telegra.ph/file/91533956c91d0fd7c9f20.jpg":
        if not _re.match("(?:http|https)://", PING_IMG_URL):
            print(
                "[ERROR] - Your PING_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()

if PLAYLIST_IMG_URL:
    if PLAYLIST_IMG_URL != "https://telegra.ph/file/f4edfbd83ec3150284aae.jpg":
        if not _re.match("(?:http|https)://", PLAYLIST_IMG_URL):
            print(
                "[ERROR] - Your PLAYLIST_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()

if GLOBAL_IMG_URL:
    if GLOBAL_IMG_URL != "https://telegra.ph/file/de1db74efac1770b1e8e9.jpg":
        if not _re.match("(?:http|https)://", GLOBAL_IMG_URL):
            print(
                "[ERROR] - Your GLOBAL_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()


if STATS_IMG_URL:
    if STATS_IMG_URL != "https://telegra.ph/file/4dd9e2c231eaf7c290404.jpg":
        if not _re.match("(?:http|https)://", STATS_IMG_URL):
            print(
                "[ERROR] - Your STATS_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()


if TELEGRAM_AUDIO_URL:
    if TELEGRAM_AUDIO_URL != "https://telegra.ph/file/8234d704952738ebcda7f.jpg":
        if not _re.match("(?:http|https)://", TELEGRAM_AUDIO_URL):
            print(
                "[ERROR] - Your TELEGRAM_AUDIO_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()


if STREAM_IMG_URL:
    if STREAM_IMG_URL != "https://telegra.ph/file/e24f4a5f695ec5576a8f3.jpg":
        if not _re.match("(?:http|https)://", STREAM_IMG_URL):
            print(
                "[ERROR] - Your STREAM_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()


if SOUNCLOUD_IMG_URL:
    if SOUNCLOUD_IMG_URL != "https://telegra.ph/file/7645d1e04021323c21db9.jpg":
        if not _re.match("(?:http|https)://", SOUNCLOUD_IMG_URL):
            print(
                "[ERROR] - Your SOUNCLOUD_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()

if YOUTUBE_IMG_URL:
    if YOUTUBE_IMG_URL != "https://telegra.ph/file/76d29aa31c40a7f026d7e.jpg":
        if not _re.match("(?:http|https)://", YOUTUBE_IMG_URL):
            print(
                "[ERROR] - Your YOUTUBE_IMG_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()


if TELEGRAM_VIDEO_URL:
    if TELEGRAM_VIDEO_URL != "https://telegra.ph/file/8d02ff3bde400e465219a.jpg":
        if not _re.match("(?:http|https)://", TELEGRAM_VIDEO_URL):
            print(
                "[ERROR] - Your TELEGRAM_VIDEO_URL url is wrong. Please ensure that it starts with https://"
            )
            _sys.exit()
