import os
import config
from pyrogram import filters
from pyrogram.types import Message
from Amusic import app
from Amusic.misc import SUDOERS

IMAGE_TEXT_FILE = "strings/image_text.txt"

def get_current_text():
    """الحصول على النص الحالي للصورة"""
    try:
        if os.path.exists(IMAGE_TEXT_FILE):
            with open(IMAGE_TEXT_FILE, 'r', encoding='utf-8') as f:
                return f.read().strip()
        return "A-MUSIC BOT"
    except:
        return "A-MUSIC BOT"

def save_image_text(text):
    """حفظ النص الجديد للصورة"""
    try:
        os.makedirs(os.path.dirname(IMAGE_TEXT_FILE), exist_ok=True)
        with open(IMAGE_TEXT_FILE, 'w', encoding='utf-8') as f:
            f.write(text)
        return True
    except:
        return False

@app.on_message(filters.command("تغيير_نص_الصورة") & SUDOERS)
async def change_image_text_command(client, message: Message):
    """أمر تغيير نص الصورة"""
    try:
        if len(message.command) < 2:
            current_text = get_current_text()
            await message.reply_text(
                f"**📝 تغيير نص الصورة**\n\n"
                f"**النص الحالي:** `{current_text}`\n\n"
                f"**الاستخدام:**\n"
                f"`/تغيير_نص_الصورة [النص الجديد]`\n\n"
                f"**مثال:**\n"
                f"`/تغيير_نص_الصورة MY MUSIC BOT`"
            )
            return

        new_text = " ".join(message.command[1:])
        
        if len(new_text) > 50:
            await message.reply_text(
                "❌ **خطأ!**\n\n"
                "النص طويل جداً. يجب أن يكون أقل من 50 حرف."
            )
            return

        if save_image_text(new_text):
            await message.reply_text(
                f"✅ **تم تغيير نص الصورة بنجاح!**\n\n"
                f"**النص الجديد:** `{new_text}`\n\n"
                f"سيظهر النص الجديد في جميع صور الأغاني القادمة."
            )
        else:
            await message.reply_text(
                "❌ **خطأ!**\n\n"
                "فشل في حفظ النص الجديد. تأكد من صلاحيات الكتابة."
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ!**\n\n"
            f"حدث خطأ أثناء تغيير النص: `{str(e)}`"
        )

@app.on_message(filters.command("عرض_نص_الصورة") & SUDOERS)
async def show_image_text_command(client, message: Message):
    """أمر عرض النص الحالي للصورة"""
    try:
        current_text = get_current_text()
        await message.reply_text(
            f"**📝 النص الحالي للصورة**\n\n"
            f"`{current_text}`\n\n"
            f"لتغيير النص استخدم:\n"
            f"`/تغيير_نص_الصورة [النص الجديد]`"
        )
    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ!**\n\n"
            f"فشل في عرض النص: `{str(e)}`"
        )

@app.on_message(filters.command("استعادة_نص_الصورة") & SUDOERS)
async def reset_image_text_command(client, message: Message):
    """أمر استعادة النص الافتراضي للصورة"""
    try:
        default_text = "A-MUSIC BOT"
        
        if save_image_text(default_text):
            await message.reply_text(
                f"✅ **تم استعادة النص الافتراضي!**\n\n"
                f"**النص:** `{default_text}`"
            )
        else:
            await message.reply_text(
                "❌ **خطأ!**\n\n"
                "فشل في استعادة النص الافتراضي."
            )
    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ!**\n\n"
            f"حدث خطأ: `{str(e)}`"
        )
