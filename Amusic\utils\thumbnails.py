import os
import requests
import config
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

from py_yt import VideosSearch


def changeImageSize(maxWidth, maxHeight, image):
    """تغيير حجم الصورة مع الحفاظ على النسبة"""
    widthRatio = maxWidth / image.size[0]
    heightRatio = maxHeight / image.size[1]
    newWidth = int(widthRatio * image.size[0])
    newHeight = int(heightRatio * image.size[1])

    if newWidth > maxWidth:
        newWidth = maxWidth
    if newHeight > maxHeight:
        newHeight = maxHeight

    return image.resize((newWidth, newHeight))


def clean_text_for_image(text):
    """تنظيف النص من الأحرف التي قد تسبب مشاكل في الترميز"""
    if not text:
        return "Unknown"

    try:
        
        text = str(text)

        
        emoji_chars = ['🎵', '🎧', '🎤', '♪', '♫', '♬', '🎶', '🎼', '🎹', '🥁', '🎸', '🎺', '🎷', '🎻']
        for emoji in emoji_chars:
            text = text.replace(emoji, '')

        
        special_chars = ['⏱️', '🕐', '👁️', '👀', '📺', '📻', '▶️', '⏸️', '⏹️', '⏭️', '⏮️']
        for char in special_chars:
            text = text.replace(char, '')

        
        text = ' '.join(text.split())

        
        if not text.strip():
            return "Unknown"

        
        try:
            text.encode('utf-8')
            return text
        except UnicodeEncodeError:
            
            safe_text = text.encode('ascii', 'ignore').decode('ascii')
            return safe_text if safe_text.strip() else "Unknown"

    except Exception:
        return "Unknown"


def hybrid_translate(text):
    """ترجمة النص (يمكن تحسينها لاحقاً)"""
    try:
        
        text = clean_text_for_image(text)

        
        translations = {
            "أغنية": "Song",
            "فيديو": "Video",
            "موسيقى": "Music",
            "مقطع": "Clip",
            "بث": "Stream",
            "مباشر": "Live",
            "قناة": "Channel",
            "مشاهدات": "Views",
            "مدة": "Duration",
            "ثانية": "sec",
            "دقيقة": "min",
            "ساعة": "hour"
        }

        
        for arabic, english in translations.items():
            text = text.replace(arabic, english)

        return text
    except:
        return "Unknown"


async def create_song_thumbnail(
    client,
    song_title: str,
    duration: str,
    views: str,
    channel_name: str,
    thumbnail_url: str,
    output_path: str = None
):
    """
    إنشاء صورة مخصصة للأغنية

    Args:
        client: عميل البوت
        song_title: عنوان الأغنية
        duration: مدة الأغنية
        views: عدد المشاهدات
        channel_name: اسم القناة
        thumbnail_url: رابط صورة الأغنية
        output_path: مسار حفظ الصورة (اختياري)

    Returns:
        str: مسار الصورة المحفوظة أو None في حالة الخطأ
    """

    try:
        
        if not output_path:
            output_path = f"downloads/thumbnail_{hash(song_title)}.png"

        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        
        try:
            response = requests.get(thumbnail_url, timeout=10)
            response.raise_for_status()
            img = Image.open(BytesIO(response.content))
        except Exception as e:
            print(f"❌ خطأ في تحميل صورة الأغنية: {e}")
            return None

        
        try:
            
            image1 = changeImageSize(1280, 720, img)
            image2 = image1.convert("RGBA")

            
            background = image2.filter(ImageFilter.BoxBlur(10))
            enhancer = ImageEnhance.Brightness(background)
            background = enhancer.enhance(0.6)

            
            draw = ImageDraw.Draw(background)

            
            box_size = (500, 500)
            box_position = (40, 100)
            box_image = Image.new("RGBA", box_size, (255, 255, 255, 0))
            box_draw = ImageDraw.Draw(box_image)

            
            try:
                OWNER_ID = config.OWNER_ID[0]
                user_info = await client.get_chat(OWNER_ID)

                if user_info.photo:
                    user_photo_path = await client.download_media(user_info.photo.big_file_id)
                    inner_image = Image.open(user_photo_path).resize((460, 460))

                    
                    shadow = Image.new("RGBA", (480, 480), (0, 0, 0, 0))
                    shadow_draw = ImageDraw.Draw(shadow)

                    
                    shadow_draw.rectangle([(15, 15), (475, 475)], fill=(0, 0, 0, 100))

                    
                    shadow = shadow.filter(ImageFilter.GaussianBlur(radius=8))

                    
                    box_image.paste(shadow, (0, 0), shadow)

                    
                    box_image.paste(inner_image, (10, 10))

                    
                    frame_draw = ImageDraw.Draw(box_image)
                    frame_draw.rectangle([(8, 8), (472, 472)], outline="white", width=8)

                    
                    if os.path.exists(user_photo_path):
                        os.remove(user_photo_path)
                else:
                    
                    default_image = Image.new("RGB", (460, 460), (80, 80, 80))

                    
                    for y in range(460):
                        color_value = int(80 + (y / 460) * 40)  
                        for x in range(460):
                            default_image.putpixel((x, y), (color_value, color_value, color_value))

                    
                    shadow = Image.new("RGBA", (480, 480), (0, 0, 0, 0))
                    shadow_draw = ImageDraw.Draw(shadow)
                    shadow_draw.rectangle([(15, 15), (475, 475)], fill=(0, 0, 0, 100))
                    shadow = shadow.filter(ImageFilter.GaussianBlur(radius=8))

                    
                    box_image.paste(shadow, (0, 0), shadow)
                    box_image.paste(default_image, (10, 10))

                    
                    frame_draw = ImageDraw.Draw(box_image)
                    frame_draw.rectangle([(8, 8), (472, 472)], outline="white", width=4)

            except Exception as e:
                print(f"⚠️ خطأ في الحصول على صورة المطور: {e}")
                
                default_image = Image.new("RGB", (460, 460), (80, 80, 80))

                
                for y in range(460):
                    color_value = int(80 + (y / 460) * 40)
                    for x in range(460):
                        default_image.putpixel((x, y), (color_value, color_value, color_value))

                
                shadow = Image.new("RGBA", (480, 480), (0, 0, 0, 0))
                shadow_draw = ImageDraw.Draw(shadow)
                shadow_draw.rectangle([(15, 15), (475, 475)], fill=(0, 0, 0, 100))
                shadow = shadow.filter(ImageFilter.GaussianBlur(radius=8))

                
                box_image.paste(shadow, (0, 0), shadow)
                box_image.paste(default_image, (10, 10))

                
                frame_draw = ImageDraw.Draw(box_image)
                frame_draw.rectangle([(8, 8), (472, 472)], outline="white", width=4)

            
            background.paste(box_image, box_position, box_image)

            
            font_small = None
            font_large = None

            
            font_paths = [
                ("strings/font.ttf", "strings/MazzardH-Black.otf"),
            ]

            for small_path, large_path in font_paths:
                try:
                    font_small = ImageFont.truetype(small_path, 50)
                    font_large = ImageFont.truetype(large_path, 90)
                    break
                except Exception as e:
                    continue

            
            if font_small is None or font_large is None:
                try:
                    font_small = ImageFont.load_default()
                    font_large = ImageFont.load_default()
                except Exception:
                    return None


            draw = ImageDraw.Draw(background)

            try:
                if os.path.exists("strings/image_text.txt"):
                    with open("strings/image_text.txt", 'r', encoding='utf-8') as f:
                        bot_title = f.read().strip()
                else:
                    bot_title = "A-MUSIC BOT"
            except:
                bot_title = "A-MUSIC BOT"

            draw.text((600, 100), bot_title, fill="white", font=font_large)

            
            y_position = 250

            if duration:
                try:
                    draw.text((570, y_position), f"Duration: {duration}", fill="white", font=font_small)
                    y_position += 80
                except UnicodeEncodeError:
                    try:
                        safe_duration = str(duration).encode('ascii', 'ignore').decode('ascii')
                        if safe_duration.strip():
                            draw.text((570, y_position), f"Duration: {safe_duration}", fill="white", font=font_small)
                            y_position += 80
                    except:
                        pass

            if views:
                try:
                    draw.text((570, y_position), f"Views: {views}", fill="white", font=font_small)
                    y_position += 80
                except UnicodeEncodeError:
                    try:
                        safe_views = str(views).encode('ascii', 'ignore').decode('ascii')
                        if safe_views.strip():
                            draw.text((570, y_position), f"Views: {safe_views}", fill="white", font=font_small)
                            y_position += 80
                    except:
                        pass

            if channel_name:
                try:
                    draw.text((570, y_position), f"Channel: {channel_name}", fill="white", font=font_small)
                except UnicodeEncodeError:
                    try:
                        safe_channel = str(channel_name).encode('ascii', 'ignore').decode('ascii')
                        if safe_channel.strip():
                            draw.text((570, y_position), f"Channel: {safe_channel}", fill="white", font=font_small)
                        else:
                            draw.text((570, y_position), "Channel: Unknown", fill="white", font=font_small)
                    except:
                        draw.text((570, y_position), "Channel: Unknown", fill="white", font=font_small)

            background.save(output_path, "PNG")

        except Exception as e:
            print(f"❌ خطأ في معالجة الصورة: {e}")
            return None

        return output_path

    except Exception as e:
        print(f"❌ خطأ عام في إنشاء الصورة: {e}")
        return None



async def gen_thumb(videoid, thumb=None):
    if thumb:
        return thumb
    try:
        query = f"https://www.youtube.com/watch?v={videoid}"
        results = VideosSearch(query, limit=1)
        for result in (await results.next())["result"]:
            thumbnail = result["thumbnails"][0]["url"].split("?")[0]
        return thumbnail
    except Exception:
        return f"https://img.youtube.com/vi/{videoid}/maxresdefault.jpg"


async def gen_qthumb(vidid, thumb=None):
    if thumb:
        return thumb
    try:
        query = f"https://www.youtube.com/watch?v={vidid}"
        results = VideosSearch(query, limit=1)
        for result in (await results.next())["result"]:
            thumbnail = result["thumbnails"][0]["url"].split("?")[0]
        return thumbnail
    except Exception:
        return f"https://img.youtube.com/vi/{vidid}/maxresdefault.jpg"
