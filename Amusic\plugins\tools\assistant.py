



import time
import asyncio
from pyrogram import filters
from pyrogram.types import InlineKeyboardButton, InlineKeyboardMarkup, Message
from pyrogram.errors import UsernameNotOccupied, UsernameOccupied, FloodWait

import config
from strings import command
from Amusic import app
from Amusic.misc import _boot_, SUDOERS
from Amusic.utils.database import get_assistant, get_lang
from Amusic.utils.decorators.language import language
from Amusic.utils.formatters import get_readable_time
from strings import get_string



@app.on_message(command("ASSISTANT_COMMAND") & filters.group & SUDOERS)
@language
async def assistant_command(client, message: Message, _):
    """عرض لوحة تحكم المساعد - للمطورين فقط"""
    try:
        
        assistant = await get_assistant(message.chat.id)
        
        if not assistant:
            return await message.reply_text("❌ **لا يوجد مساعد متاح حالياً**")
        
        
        assistant_info = await assistant.get_me()
        
        
        try:
            await assistant.get_me()
            status = _["assistant_status_online"]
            connected_since = get_readable_time(int(time.time() - _boot_))
        except Exception:
            status = _["assistant_status_offline"]
            connected_since = "غير متصل"
        
        
        name = f"{assistant_info.first_name} {assistant_info.last_name or ''}".strip()
        user_id = assistant_info.id
        username = assistant_info.username or _["assistant_no_username"]
        
        
        try:
            bio = assistant_info.about or _["assistant_no_bio"]
        except Exception:
            bio = _["assistant_no_bio"]
        
        
        try:
            photos = assistant_info.phone_number  
            has_photo = _["assistant_has_photo"] if photos.total_count > 0 else _["assistant_no_photo"]
        except Exception:
            has_photo = _["assistant_no_photo"]
        
        
        text = _["assistant_1"].format(
            name, user_id, username, bio, has_photo, status, connected_since
        )
        
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👤 تغيير الاسم", callback_data="assistant_change_name"),
                InlineKeyboardButton("📝 تغيير البايو", callback_data="assistant_change_bio")
            ],
            [
                InlineKeyboardButton("📱 تغيير اليوزر", callback_data="assistant_change_username"),
                InlineKeyboardButton("🖼️ تغيير الصورة", callback_data="assistant_change_photo")
            ],
            [
                InlineKeyboardButton("🗑️ حذف الصورة", callback_data="assistant_remove_photo"),
                InlineKeyboardButton("🔄 تحديث المعلومات", callback_data="assistant_refresh")
            ],
            [
                InlineKeyboardButton("📢 القناة", url=config.SUPPORT_CHANNEL if config.SUPPORT_CHANNEL else "https://t.me/your_channel"),
                InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{config.OWNER_ID[0]}" if config.OWNER_ID else "https://t.me/your_developer")
            ],
            [
                InlineKeyboardButton("❌ إغلاق", callback_data="close")
            ]
        ])
        
        await message.reply_text(text, reply_markup=keyboard)
        
    except Exception as e:
        await message.reply_text(f"❌ **خطأ في الحصول على معلومات المساعد:**\n\n`{str(e)}`")



@app.on_callback_query(filters.regex(r"^assistant_") & SUDOERS)
async def assistant_callbacks(client, query):
    """معالج كولباك أوامر المساعد - للمطورين فقط"""
    try:
        callback_data = query.data
        chat_id = query.message.chat.id
        
        
        assistant = await get_assistant(chat_id)
        if not assistant:
            return await query.answer("❌ لا يوجد مساعد متاح", show_alert=True)
        
        
        try:
            language = await get_lang(chat_id)
            _ = get_string(language)
        except Exception:
            _ = get_string("ar")
        
        if callback_data == "assistant_refresh":
            
            await refresh_assistant_info(query, assistant, _)
            
        elif callback_data == "assistant_change_name":
            await change_assistant_name(query, assistant, _)
            
        elif callback_data == "assistant_change_bio":
            await change_assistant_bio(query, assistant, _)
            
        elif callback_data == "assistant_change_username":
            await change_assistant_username(query, assistant, _)
            
        elif callback_data == "assistant_change_photo":
            await change_assistant_photo(query, assistant, _)
            
        elif callback_data == "assistant_remove_photo":
            await remove_assistant_photo(query, assistant, _)
            
    except Exception as e:
        await query.answer(f"❌ خطأ: {str(e)}", show_alert=True)


async def refresh_assistant_info(query, assistant, _):
    """تحديث معلومات المساعد"""
    try:
        
        assistant_info = await assistant.get_me()
        
        
        try:
            await assistant.get_me()
            status = _["assistant_status_online"]
            connected_since = get_readable_time(int(time.time() - _boot_))
        except Exception:
            status = _["assistant_status_offline"]
            connected_since = "غير متصل"
        
        
        name = f"{assistant_info.first_name} {assistant_info.last_name or ''}".strip()
        user_id = assistant_info.id
        username = assistant_info.username or _["assistant_no_username"]
        
        
        try:
            full_user = await assistant.get_users(assistant_info.id)
            bio = getattr(full_user, 'bio', None) or _["assistant_no_bio"]
        except Exception:
            bio = _["assistant_no_bio"]
        
        
        try:
            photos = await assistant.get_chat_photos(assistant_info.id, limit=1)
            has_photo = _["assistant_has_photo"] if photos.total_count > 0 else _["assistant_no_photo"]
        except Exception:
            has_photo = _["assistant_no_photo"]
        
        
        text = _["assistant_1"].format(
            name, user_id, username, bio, has_photo, status, connected_since
        )
        
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("👤 تغيير الاسم", callback_data="assistant_change_name"),
                InlineKeyboardButton("📝 تغيير البايو", callback_data="assistant_change_bio")
            ],
            [
                InlineKeyboardButton("📱 تغيير اليوزر", callback_data="assistant_change_username"),
                InlineKeyboardButton("🖼️ تغيير الصورة", callback_data="assistant_change_photo")
            ],
            [
                InlineKeyboardButton("🗑️ حذف الصورة", callback_data="assistant_remove_photo"),
                InlineKeyboardButton("🔄 تحديث المعلومات", callback_data="assistant_refresh")
            ],
            [
                InlineKeyboardButton("📢 القناة", url=config.SUPPORT_CHANNEL if config.SUPPORT_CHANNEL else "https://t.me/your_channel"),
                InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{config.OWNER_ID[0]}" if config.OWNER_ID else "https://t.me/your_developer")
            ],
            [
                InlineKeyboardButton("❌ إغلاق", callback_data="close")
            ]
        ])
        
        await query.edit_message_text(text, reply_markup=keyboard)
        await query.answer("✅ تم تحديث المعلومات")

    except Exception as e:
        await query.answer(f"❌ خطأ في التحديث: {str(e)}", show_alert=True)


async def change_assistant_name(query, assistant, _):
    """تغيير اسم المساعد"""
    try:
        await query.answer()

        
        msg = await query.message.reply_text(_["assistant_send_name"])

        
        try:
            response = await app.listen(query.message.chat.id, filters=filters.text, timeout=60)

            if response.text.lower() in ["/cancel", "إلغاء", "cancel"]:
                await msg.delete()
                await response.delete()
                return await query.message.reply_text(_["assistant_cancelled"])

            
            name_parts = response.text.strip().split(" ", 1)
            first_name = name_parts[0]
            last_name = name_parts[1] if len(name_parts) > 1 else ""

            
            await assistant.update_profile(first_name=first_name, last_name=last_name)

            
            await msg.delete()
            await response.delete()

            
            full_name = f"{first_name} {last_name}".strip()
            await query.message.reply_text(_["assistant_name_changed"].format(full_name))

        except asyncio.TimeoutError:
            await msg.delete()
            await query.message.reply_text(_["assistant_timeout"])

    except Exception as e:
        await query.message.reply_text(_["assistant_error_name"].format(str(e)))


async def change_assistant_bio(query, assistant, _):
    """تغيير بايو المساعد"""
    try:
        await query.answer()

        
        msg = await query.message.reply_text(_["assistant_send_bio"])

        
        try:
            response = await app.listen(query.message.chat.id, filters=filters.text, timeout=60)

            if response.text.lower() in ["/cancel", "إلغاء", "cancel"]:
                await msg.delete()
                await response.delete()
                return await query.message.reply_text(_["assistant_cancelled"])

            new_bio = response.text.strip()

            
            if len(new_bio) > 70:
                await msg.delete()
                await response.delete()
                return await query.message.reply_text("❌ **البايو طويل جداً!**\n\n⚠️ **الحد الأقصى 70 حرف**")

            
            await assistant.update_profile(bio=new_bio)

            
            await msg.delete()
            await response.delete()

            
            await query.message.reply_text(_["assistant_bio_changed"].format(new_bio))

        except asyncio.TimeoutError:
            await msg.delete()
            await query.message.reply_text(_["assistant_timeout"])

    except Exception as e:
        await query.message.reply_text(_["assistant_error_bio"].format(str(e)))


async def change_assistant_username(query, assistant, _):
    """تغيير يوزر نيم المساعد"""
    try:
        await query.answer()

        
        msg = await query.message.reply_text(_["assistant_send_username"])

        
        try:
            response = await app.listen(query.message.chat.id, filters=filters.text, timeout=60)

            if response.text.lower() in ["/cancel", "إلغاء", "cancel"]:
                await msg.delete()
                await response.delete()
                return await query.message.reply_text(_["assistant_cancelled"])

            new_username = response.text.strip().replace("@", "")

            
            if not new_username.replace("_", "").isalnum():
                await msg.delete()
                await response.delete()
                return await query.message.reply_text("❌ **يوزر نيم غير صحيح!**\n\n⚠️ **يجب أن يحتوي على أحرف وأرقام فقط**")

            
            try:
                await assistant.set_username(new_username)

                
                await msg.delete()
                await response.delete()

                
                await query.message.reply_text(_["assistant_username_changed"].format(new_username))

            except UsernameOccupied:
                await msg.delete()
                await response.delete()
                await query.message.reply_text("❌ **اليوزر نيم مستخدم بالفعل!**\n\n💡 **جرب يوزر نيم آخر**")

        except asyncio.TimeoutError:
            await msg.delete()
            await query.message.reply_text(_["assistant_timeout"])

    except Exception as e:
        await query.message.reply_text(_["assistant_error_username"].format(str(e)))


async def change_assistant_photo(query, assistant, _):
    """تغيير صورة المساعد"""
    try:
        await query.answer()

        
        msg = await query.message.reply_text(_["assistant_send_photo"])

        
        try:
            response = await app.listen(query.message.chat.id, filters=filters.photo | filters.document, timeout=60)

            
            if response.photo:
                await assistant.set_profile_photo(photo=response.photo.file_id)
            elif response.document and response.document.mime_type.startswith("image/"):
                await assistant.set_profile_photo(photo=response.document.file_id)
            else:
                await msg.delete()
                await response.delete()
                return await query.message.reply_text("❌ **يرجى إرسال صورة صحيحة!**")

            
            await msg.delete()
            await response.delete()

            
            await query.message.reply_text(_["assistant_photo_changed"])

        except asyncio.TimeoutError:
            await msg.delete()
            await query.message.reply_text(_["assistant_timeout"])

    except Exception as e:
        await query.message.reply_text(_["assistant_error_photo"].format(str(e)))


async def remove_assistant_photo(query, assistant, _):
    """حذف صورة المساعد"""
    try:
        await query.answer()

        
        try:
            photos = await assistant.get_chat_photos("me")
            async for photo in photos:
                await assistant.delete_profile_photos(photo.file_id)

            await query.message.reply_text(_["assistant_photo_removed"])

        except Exception as e:
            await query.message.reply_text(_["assistant_error_photo"].format(str(e)))

    except Exception as e:
        await query.message.reply_text(_["assistant_error_photo"].format(str(e)))
